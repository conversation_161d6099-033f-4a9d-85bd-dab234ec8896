{"name": "framefuse-api", "version": "1.0.0", "description": "FrameFuse API for video rendering with FFmpeg", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "cors": "^2.8.5", "express": "^4.18.2", "multer": "^2.0.2", "sharp": "^0.33.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}, "keywords": ["video", "ffmpeg", "rendering", "api", "gitlab"], "author": "FrameFuse Team", "license": "MIT"}