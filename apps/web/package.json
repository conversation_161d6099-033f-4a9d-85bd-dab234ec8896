{"name": "web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"prebuild": "pnpm -w build --filter=web^...", "dev": "vite", "build": "tsc -p tsconfig.json && vite build", "preview": "vite preview", "lint": "eslint 'src/**/*.{ts,tsx}'", "test": "vitest", "typecheck": "tsc -p tsconfig.json --noEmit"}, "dependencies": {"@framefuse/core": "workspace:*", "@framefuse/ffmpeg-worker": "workspace:*", "@framefuse/ui-kit": "workspace:*", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fontsource/jetbrains-mono": "^5.0.21", "@tanstack/react-query": "^5.51.1", "@vercel/blob": "^1.1.1", "idb-keyval": "^6.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.5.2"}, "devDependencies": {"@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.5.0", "fake-indexeddb": "^6.0.0", "jsdom": "^25.0.0", "eslint": "^9.7.0", "postcss": "^8.4.39", "tailwindcss": "^3.4.7", "typescript": "^5.4.5", "vite": "^5.3.4", "vitest": "^1.6.0", "fflate": "^0.8.1"}}