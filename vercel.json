{"build": {"env": {"NODE_VERSION": "20"}}, "installCommand": "pnpm install --frozen-lockfile", "buildCommand": "mkdir -p public && echo 'FrameFuse API' > public/index.html", "outputDirectory": "public", "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}