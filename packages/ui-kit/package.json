{"name": "@framefuse/ui-kit", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "scripts": {"build": "tsc -p tsconfig.build.json", "dev": "tsc -w -p tsconfig.build.json", "lint": "eslint 'src/**/*.{ts,tsx}'", "test": "vitest", "typecheck": "tsc -p tsconfig.build.json --noEmit"}, "devDependencies": {"typescript": "^5.4.5", "vitest": "^1.6.0", "eslint": "^9.7.0", "@typescript-eslint/parser": "^7.8.0", "@typescript-eslint/eslint-plugin": "^7.8.0", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.5.0", "jsdom": "^25.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}}