{"name": "@framefuse/ffmpeg-worker", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "scripts": {"build": "tsc -p tsconfig.build.json", "dev": "tsc -w -p tsconfig.build.json", "lint": "eslint 'src/**/*.{ts,tsx}'", "test": "vitest", "typecheck": "tsc -p tsconfig.build.json --noEmit"}, "dependencies": {"@ffmpeg/ffmpeg": "^0.12.10", "@framefuse/core": "workspace:*"}, "devDependencies": {"typescript": "^5.4.5", "vitest": "^1.6.0", "eslint": "^9.7.0", "@typescript-eslint/parser": "^7.8.0", "@typescript-eslint/eslint-plugin": "^7.8.0"}}