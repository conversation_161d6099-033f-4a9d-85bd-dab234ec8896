{"compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022", "DOM"], "moduleResolution": "<PERSON><PERSON><PERSON>", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./", "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@framefuse/*": ["./packages/*/src"]}, "types": ["node", "vite/client"], "typeRoots": ["./node_modules/@types"]}, "include": ["api/**/*", "src/**/*", "packages/**/*", "scripts/**/*", "*.ts", "*.tsx"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}